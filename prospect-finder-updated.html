<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>Prospect Finder</title>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
    <style type="text/tailwindcss">
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f1f5f9;
        }
    </style>
</head>
<body>
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <!-- Left side - Logo and Project Badge -->
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gray-900 rounded-lg flex items-center justify-center">
                                <span class="material-icons text-white text-lg">search</span>
                            </div>
                            <h1 class="text-lg font-semibold text-gray-900">Prospect Finder</h1>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                            <span class="material-icons text-xs mr-1">folder</span>
                            Project: ADMIN
                        </span>
                    </div>
                    
                    <!-- Right side - User info and actions -->
                    <div class="flex items-center space-x-4">
                        <button class="text-sm font-medium text-gray-600 hover:text-gray-900 px-3 py-1.5 rounded-md hover:bg-gray-50">
                            Admin
                        </button>
                        <div class="flex items-center space-x-2">
                            <div class="h-7 w-7 rounded-full bg-blue-100 flex items-center justify-center">
                                <span class="text-xs font-medium text-blue-700">PK</span>
                            </div>
                            <span class="text-sm font-medium text-gray-700">Prashant Kanyal</span>
                        </div>
                        <button class="text-sm font-medium text-gray-600 hover:text-gray-900 px-3 py-1.5 rounded-md hover:bg-gray-50">
                            Logout
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-6">
            <!-- Search Section -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-6">Search Prospects</h2>
                
                <!-- Tab Navigation -->
                <div class="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6 w-fit">
                    <button class="flex items-center px-4 py-2 text-sm font-medium rounded-md bg-white text-blue-600 shadow-sm border border-gray-200">
                        <span class="material-icons text-sm mr-2">search</span>
                        Search by Prospect Info
                    </button>
                    <button class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50">
                        <span class="material-icons text-sm mr-2">link</span>
                        Search by LinkedIn URL
                    </button>
                </div>

                <!-- Form Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2" for="prospect-name">
                            Prospect Name
                        </label>
                        <input 
                            class="w-full px-3 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm" 
                            id="prospect-name" 
                            type="text" 
                            value="Aakash Bhatnagar"
                        />
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2" for="company-name">
                            Company Name
                        </label>
                        <input 
                            class="w-full px-3 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm" 
                            id="company-name" 
                            type="text" 
                            value="Macawber Beekay Pvt Ltd."
                        />
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2" for="location">
                            Location <span class="text-gray-500 font-normal">(optional)</span>
                        </label>
                        <input 
                            class="w-full px-3 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm" 
                            id="location" 
                            placeholder="City, State or leave blank" 
                            type="text"
                        />
                    </div>
                </div>

                <!-- Search Button -->
                <div class="flex justify-end mt-6">
                    <button class="inline-flex items-center px-6 py-2.5 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                        <span class="material-icons text-sm mr-2">search</span>
                        Search
                    </button>
                </div>
            </div>

            <!-- Results Section -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-lg font-semibold text-gray-900">Search Results</h2>
                    <button class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-sm transition-colors">
                        <span class="material-icons text-sm mr-2">content_copy</span>
                        Copy All Results
                    </button>
                </div>

                <!-- Results Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-gray-200">
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone Numbers</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">LinkedIn</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-100">
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                                            <span class="text-xs font-medium text-gray-600">AB</span>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">Aakash Bhatnagar</span>
                                    </div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-600">Macawber Beekay Pvt Ltd.</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-600"><EMAIL></td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-600">931...</td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <a href="#" class="inline-flex items-center text-sm text-blue-600 hover:text-blue-700">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.389 0-1.601 1.086-1.601 2.206v4.249H8.014V8h2.53v1.16h.036c.355-.675 1.227-1.387 2.5-1.387 2.671 0 3.163 1.756 3.163 4.039v4.686zM5.037 6.875a1.844 1.844 0 11-.002-3.688 1.844 1.844 0 01.002 3.688zM6.35 16.338H3.72V8h2.63v8.338zM18 0H2a2 2 0 00-2 2v16a2 2 0 002 2h16a2 2 0 002-2V2a2 2 0 00-2-2z"/>
                                        </svg>
                                        Profile
                                    </a>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-600">Noida</td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <button class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 shadow-sm">
                                        <span class="material-icons text-sm mr-1">content_copy</span>
                                        Copy
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
